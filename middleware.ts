import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  const response = NextResponse.next();

  // Apply cache prevention headers to all routes except static assets
  const isStaticAsset =
    request.nextUrl.pathname.startsWith("/_next/static") ||
    request.nextUrl.pathname.startsWith("/favicon") ||
    /\.(svg|png|jpg|jpeg|gif|webp|ico|woff|woff2|ttf|eot)$/.test(
      request.nextUrl.pathname
    );

  // Apply no-cache headers to all routes except static assets
  const shouldPreventCache = !isStaticAsset;

  if (shouldPreventCache) {
    // Comprehensive cache prevention headers
    response.headers.set(
      "Cache-Control",
      "no-cache, no-store, must-revalidate, private, max-age=0"
    );
    response.headers.set("Pragma", "no-cache");
    response.headers.set("Expires", "0");

    // Additional security headers for sensitive content
    response.headers.set("X-Content-Type-Options", "nosniff");
    response.headers.set("X-Frame-Options", "SAMEORIGIN");
    response.headers.set("X-XSS-Protection", "1; mode=block");
    response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

    // Content Security Policy - Environment-aware CSP (matching next.config.js)
    const cspHeader = [
      "default-src 'self'",
      `script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: https://www.googletagmanager.com https://www.google-analytics.com https://cmp.osano.com https://*.osano.com https://www.gstatic.com https://www.google.com https://www.recaptcha.net https://snap.licdn.com https://www.linkedin.com https://*.ads.linkedin.com *`,
      // More restrictive worker-src
      `worker-src 'self' blob: *`,
      `style-src 'self' 'unsafe-inline' https://fonts.googleapis.com *`,
      "font-src 'self' https://fonts.gstatic.com data:",
      `img-src 'self' data: blob: https: http: *`,
      `connect-src 'self' https://www.google-analytics.com https://region1.google-analytics.com https://analytics.google.com https://stats.g.doubleclick.net https://www.googletagmanager.com https://www.google.com https://google.com https://ipapi.co https://cmp.osano.com https://*.osano.com https://*.amazonaws.com https://*.amplifyapp.com https://*.apphero.io https://px.ads.linkedin.com https://www.linkedin.com https://*.linkedin.com *`,
      "frame-src 'self' https://www.google.com https://www.recaptcha.net https://recaptcha.google.com https://www.googletagmanager.com https://td.doubleclick.net https://*.doubleclick.net https://*.apphero.io https://*.amplifyapp.com",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'self'",
      "upgrade-insecure-requests",
    ].join("; ");

    response.headers.set("Content-Security-Policy", cspHeader);

    // Prevent content from being stored in browser history
    response.headers.set("Vary", "Authorization, Accept-Encoding, User-Agent");
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
